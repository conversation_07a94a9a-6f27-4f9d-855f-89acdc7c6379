<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="LaravelPint">
    <laravel_pint_settings>
      <LaravelPintConfiguration tool_path="$PROJECT_DIR$/worldoutside/vendor/bin/pint" />
    </laravel_pint_settings>
  </component>
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpCodeSniffer">
    <phpcs_settings>
      <phpcs_by_interpreter asDefaultInterpreter="true" interpreter_id="34903ca5-9cf1-464e-b712-d7dc0e40b55e" timeout="30000" />
    </phpcs_settings>
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/worldoutside/vendor/myclabs/deep-copy" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/openspout/openspout" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/guzzlehttp/uri-template" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/guzzlehttp/guzzle" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/blade-ui-kit/blade-icons" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/guzzlehttp/psr7" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/blade-ui-kit/blade-heroicons" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/guzzlehttp/promises" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/deprecation-contracts" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/string" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/service-contracts" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/mime" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/var-dumper" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-intl-idn" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-php83" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-php80" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/uid" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/routing" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/event-dispatcher" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/tinker" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/error-handler" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/framework" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/css-selector" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/sail" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-ctype" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/tables" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/sanctum" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-intl-grapheme" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/widgets" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/pint" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/event-dispatcher-contracts" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/infolists" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/serializable-closure" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/yaml" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/support" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/laravel/prompts" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/console" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/actions" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/spatie/invade" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/process" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/filament" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/nikic/php-parser" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/spatie/color" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-php72" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/notifications" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/spatie/laravel-package-tools" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-intl-normalizer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filament/forms" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/http-kernel" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/clock" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/mailer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/finder" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-uuid" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/translation" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/polyfill-mbstring" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/html-sanitizer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/translation-contracts" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/symfony/http-foundation" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/pestphp/pest" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/pestphp/pest-plugin" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/pestphp/pest-plugin-arch" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/pestphp/pest-plugin-laravel" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/tijsverkoyen/css-to-inline-styles" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/composer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/jean85/pretty-package-versions" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/flysystem" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/uri-interfaces" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/csv" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/dragonmantank/cron-expression" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/commonmark" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/uri" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/config" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpunit/php-timer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/mime-type-detection" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpunit/php-file-iterator" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/league/flysystem-local" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpunit/php-text-template" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpunit/php-invoker" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpunit/php-code-coverage" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpunit/phpunit" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpdocumentor/reflection-docblock" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/container" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpdocumentor/type-resolver" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/http-client" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpdocumentor/reflection-common" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/http-factory" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/cache" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/event-dispatcher" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/log" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/simple-cache" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psr/clock" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/ramsey/collection" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/ramsey/uuid" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/vlucas/phpdotenv" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/monolog/monolog" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/nunomaduro/collision" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/nunomaduro/termwind" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/livewire/livewire" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phar-io/manifest" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phar-io/version" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/theseer/tokenizer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/dflydev/dot-access-data" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/filp/whoops" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/brianium/paratest" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/brick/math" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/egulias/email-validator" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/psy/psysh" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/fakerphp/faker" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/ta-tikoma/phpunit-architecture-test" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/nesbot/carbon" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/fidry/cpu-core-counter" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpoption/phpoption" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/hamcrest/hamcrest-php" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/carbonphp/carbon-doctrine-types" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/mockery/mockery" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/doctrine/dbal" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/ralouphie/getallheaders" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/doctrine/inflector" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/doctrine/deprecations" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/doctrine/lexer" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/global-state" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/complexity" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/recursion-context" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/voku/portable-ascii" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/object-reflector" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/object-enumerator" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/danharrin/livewire-rate-limiting" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/cli-parser" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/danharrin/date-format-converter" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/diff" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/lines-of-code" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/type" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/masterminds/html5" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/nette/schema" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/version" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/nette/utils" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/comparator" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/exporter" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/environment" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/code-unit" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/sebastian/code-unit-reverse-lookup" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/phpstan/phpdoc-parser" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/webmozart/assert" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/ryangjchandler/blade-capture-directive" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/graham-campbell/result-type" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/anourvalar/eloquent-serialize" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/kirschbaum-development/eloquent-power-joins" />
      <path value="$PROJECT_DIR$/worldoutside/vendor/fruitcake/php-cors" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="8.2" />
  <component name="PhpStan">
    <PhpStan_settings>
      <phpstan_by_interpreter asDefaultInterpreter="true" interpreter_id="34903ca5-9cf1-464e-b712-d7dc0e40b55e" timeout="60000" />
    </PhpStan_settings>
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="Psalm">
    <Psalm_settings>
      <psalm_fixer_by_interpreter asDefaultInterpreter="true" interpreter_id="34903ca5-9cf1-464e-b712-d7dc0e40b55e" timeout="60000" />
    </Psalm_settings>
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>