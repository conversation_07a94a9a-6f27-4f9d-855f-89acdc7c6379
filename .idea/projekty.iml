<?xml version="1.0" encoding="UTF-8"?>
<module type="WEB_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/worldoutside/app" isTestSource="false" packagePrefix="App\" />
      <sourceFolder url="file://$MODULE_DIR$/worldoutside/database/factories" isTestSource="false" packagePrefix="Database\Factories\" />
      <sourceFolder url="file://$MODULE_DIR$/worldoutside/database/seeders" isTestSource="false" packagePrefix="Database\Seeders\" />
      <sourceFolder url="file://$MODULE_DIR$/worldoutside/tests" isTestSource="true" packagePrefix="Tests\" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/anourvalar/eloquent-serialize" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/blade-ui-kit/blade-heroicons" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/blade-ui-kit/blade-icons" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/brianium/paratest" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/brick/math" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/carbonphp/carbon-doctrine-types" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/composer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/danharrin/date-format-converter" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/danharrin/livewire-rate-limiting" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/dflydev/dot-access-data" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/doctrine/dbal" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/doctrine/deprecations" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/doctrine/inflector" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/doctrine/lexer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/dragonmantank/cron-expression" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/egulias/email-validator" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/fakerphp/faker" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/fidry/cpu-core-counter" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/actions" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/filament" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/forms" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/infolists" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/notifications" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/support" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/tables" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filament/widgets" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/filp/whoops" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/fruitcake/php-cors" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/graham-campbell/result-type" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/guzzlehttp/guzzle" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/guzzlehttp/promises" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/guzzlehttp/psr7" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/guzzlehttp/uri-template" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/hamcrest/hamcrest-php" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/jean85/pretty-package-versions" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/kirschbaum-development/eloquent-power-joins" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/framework" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/pint" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/prompts" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/sail" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/sanctum" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/serializable-closure" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/laravel/tinker" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/commonmark" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/config" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/csv" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/flysystem" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/flysystem-local" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/mime-type-detection" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/uri" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/league/uri-interfaces" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/livewire/livewire" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/masterminds/html5" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/mockery/mockery" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/monolog/monolog" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/myclabs/deep-copy" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/nesbot/carbon" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/nette/schema" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/nette/utils" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/nikic/php-parser" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/nunomaduro/collision" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/nunomaduro/termwind" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/openspout/openspout" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/pestphp/pest" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/pestphp/pest-plugin" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/pestphp/pest-plugin-arch" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/pestphp/pest-plugin-laravel" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phar-io/manifest" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phar-io/version" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpdocumentor/reflection-common" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpdocumentor/reflection-docblock" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpdocumentor/type-resolver" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpoption/phpoption" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpstan/phpdoc-parser" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpunit/php-code-coverage" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpunit/php-file-iterator" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpunit/php-invoker" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpunit/php-text-template" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpunit/php-timer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/phpunit/phpunit" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/cache" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/clock" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/container" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/http-client" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/http-factory" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/http-message" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/log" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psr/simple-cache" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/psy/psysh" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/ralouphie/getallheaders" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/ramsey/collection" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/ramsey/uuid" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/ryangjchandler/blade-capture-directive" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/cli-parser" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/code-unit" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/code-unit-reverse-lookup" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/comparator" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/complexity" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/diff" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/environment" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/exporter" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/global-state" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/lines-of-code" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/object-enumerator" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/object-reflector" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/recursion-context" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/type" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/sebastian/version" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/spatie/color" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/spatie/invade" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/spatie/laravel-package-tools" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/clock" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/console" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/css-selector" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/deprecation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/error-handler" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/event-dispatcher" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/event-dispatcher-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/finder" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/html-sanitizer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/http-foundation" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/http-kernel" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/mailer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/mime" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-ctype" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-intl-grapheme" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-intl-idn" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-intl-normalizer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-mbstring" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-php72" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-php80" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-php83" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/polyfill-uuid" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/process" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/routing" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/service-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/string" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/translation" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/translation-contracts" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/uid" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/var-dumper" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/symfony/yaml" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/ta-tikoma/phpunit-architecture-test" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/theseer/tokenizer" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/tijsverkoyen/css-to-inline-styles" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/vlucas/phpdotenv" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/voku/portable-ascii" />
      <excludeFolder url="file://$MODULE_DIR$/worldoutside/vendor/webmozart/assert" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>