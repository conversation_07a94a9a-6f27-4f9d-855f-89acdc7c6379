<?php

namespace Database\Factories;

use App\Models\Place;
use App\Models\PlaceUser;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class PlaceUserFactory extends Factory {
    public function definition(): array {
        return [
            'user_id' => User::factory(),
            'place_id' => Place::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }

    public function configure(): self {
        return $this->afterCreating(function (PlaceUser $placeUser) {
            $numPlaces = $this->faker->numberBetween(1, 5);
            $user = User::find($placeUser->user_id);

            $places = Place::inRandomOrder()->limit($numPlaces)->get();

            $user->places()->sync($places);
        });
    }
}
