<?php

namespace Database\Factories;

use App\Models\Place;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class PlaceFactory extends Factory {
	protected $model = Place::class;

	public function definition(): array {
		return [
			'created_at'  => Carbon::now(),
			'updated_at'  => Carbon::now(),
			'name'        => $this->faker->name(),
			'description' => $this->faker->text(),
            'latitude'    => $this->faker->latitude(47.712907, 49.626393),
            'longitude'  => $this->faker->longitude(16.843237, 22.545546),
			'image'       => $this->faker->imageUrl(),
		];
	}
}
