<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Badge;

class BadgeSeeder extends Seeder
{
    public function run(): void
    {
        Badge::updateOrCreate([
            'slug' => 'first-5-peaks',
        ], [
            'name' => 'Prvých 5 vrcholov',
            'description' => 'Navštívte 5 rôznych vrcholov.',
            'icon' => '🥾',
        ]);

        Badge::updateOrCreate([
            'slug' => 'first-2000m-peak',
        ], [
            'name' => 'Prvý dvojtisícový vrchol',
            'description' => 'Navštívte vrchol s výškou aspoň 2000 m n. m.',
            'icon' => '🏔️',
        ]);
    }
}
