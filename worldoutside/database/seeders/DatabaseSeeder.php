<?php

namespace Database\Seeders;

use App\Models\PlaceUser;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        User::factory(10)->create();
        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('test'),
        ]);

        // use command php artisan import:markers all to import markers
        Artisan::call('import:markers all');

        PlaceUser::factory(100)->create();

        $this->call(\Database\Seeders\BadgeSeeder::class);
    }
}
