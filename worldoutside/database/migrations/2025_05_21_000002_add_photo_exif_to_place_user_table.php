<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('place_user', function (Blueprint $table) {
            $table->string('photo')->nullable()->after('visit_type');
            $table->json('exif')->nullable()->after('photo');
        });
    }
};
