<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
	public function up(): void {
		Schema::create( 'places', function ( Blueprint $table ) {
			$table->id();
			$table->string( 'feature_id' )->unique()->nullable()->index();
			$table->string( 'name' );
			$table->text( 'description' );
			$table->float( 'latitude' );
			$table->float( 'longitude' );
			$table->integer( 'elevation' )->nullable(); // výška v metroch
			$table->string( 'image' );
			$table->timestamps();
		} );
	}

	public function down(): void {
		Schema::dropIfExists( 'places' );
	}
};
