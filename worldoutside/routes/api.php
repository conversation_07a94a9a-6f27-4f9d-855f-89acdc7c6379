<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::post('/login', 'App\Http\Controllers\AuthController@login')->name('login');

Route::get('/places', 'App\Http\Controllers\PlaceController@index')->name('places.index');
Route::get('/places/{latitude}/{longitude}', 'App\Http\Controllers\PlaceController@getPlaces')->name('places.get');

Route::middleware('auth:sanctum')->group( static function () {
    Route::get('/places/visited', 'App\Http\Controllers\PlaceController@getVisitedPlaces')->name('places.visited');

    Route::post('/places/{place}/visit', 'App\Http\Controllers\PlaceController@getPlaceVisit')->name('places.visit');

    Route::post('/places/submit-coordinates', 'App\Http\Controllers\PlaceController@submitUserCoordinatesForPeak')->name('places.submit-coordinates');

    // New endpoint: get visit type for a place
    Route::get('/places/{place}/visit-type', 'App\Http\Controllers\PlaceController@getVisitType')->name('places.visit-type');

    // Photo verification endpoint
    Route::post('/places/photo-verification', 'App\Http\Controllers\PlaceController@photoVerification')->name('places.photo-verification');

    // Profile endpoints
    Route::get('/profile/badges', 'App\Http\Controllers\ProfileController@badges');
    Route::get('/profile/stats', 'App\Http\Controllers\ProfileController@stats');
    Route::get('/profile', 'App\Http\Controllers\ProfileController@show');
    Route::post('/profile', 'App\Http\Controllers\ProfileController@update');

    // Planned peaks endpoints
    Route::get('/places/planned', 'App\Http\Controllers\PlaceController@getPlannedPlaces');
    Route::post('/places/{place}/plan', 'App\Http\Controllers\PlaceController@addPlanned');
    Route::post('/places/{place}/unplan', 'App\Http\Controllers\PlaceController@removePlanned');
});
