services:
    web:
        container_name: worldoutside
        build:
            target: web
            context: .
            dockerfile: Dockerfile
        env_file:
            - .env
        ports:
            - "8000:80"
        volumes:
            - .:/var/www/html
        depends_on:
            - db

    db:
        platform: "linux/amd64"
        image: postgres:15
        env_file:
            - .env
        environment:
            POSTGRES_DB: ${DB_DATABASE}
            POSTGRES_USER: ${DB_USERNAME}
            POSTGRES_PASSWORD: ${DB_PASSWORD}
        ports:
            - "5432:5432"
        volumes:
            - db-data:/var/lib/postgresql/data
volumes:
    db-data:
