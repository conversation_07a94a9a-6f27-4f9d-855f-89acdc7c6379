<?php

namespace App\Http\Controllers;

use App\Http\Resources\BadgeResource;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;

class ProfileController extends Controller
{
    public function stats(Request $request): JsonResponse
    {
        $user = $request->user('sanctum');
        $visited = $user->places()->wherePivot('visit_type', 'verified')->get();
        $count = $visited->count();
        $totalElevation = $visited->sum('elevation');
        return response()->json([
            'peaks_count' => $count,
            'total_elevation' => $totalElevation,
        ]);
    }

    public function badges(Request $request): JsonResponse
    {
        $user = $request->user('sanctum');
        return response()->json([
            'badges' => $user->badges()->get(),
        ]);
    }
    public function show(Request $request): JsonResponse
    {
        $user = $request->user('sanctum');
        return response()->json([
            'name' => $user->name,
            'email' => $user->email,
            'profile_image' => $user->profile_image,
            'bio' => $user->bio,
        ]);
    }

    public function update(Request $request): JsonResponse
    {
        $user = $request->user('sanctum');
        $data = $request->validate([
            'name' => ['sometimes', 'string', 'max:255'],
            'bio' => ['nullable', 'string', 'max:500'],
            'profile_image' => ['nullable', 'image'],
        ]);

        if ($request->hasFile('profile_image')) {
            $path = $request->file('profile_image')->store('profile-images', 'public');
            $data['profile_image'] = $path;
        }

        $user->update($data);
        return response()->json(['success' => true, 'profile_image' => $user->profile_image, 'bio' => $user->bio, 'name' => $user->name]);
    }
}
