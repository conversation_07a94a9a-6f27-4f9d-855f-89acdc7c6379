<?php

namespace App\Http\Controllers;


use Illuminate\Http\Request;

class AuthController extends Controller {
    public function login( Request $request ) {
        $credentials = $request->validate( [
            'email'    => [ 'required', 'email' ],
            'password' => [ 'required' ],
        ] );


        if ( !auth()->attempt( $credentials ) ) {
            return response()->json( [
                'message' => 'Unauthorized',
            ], 401 );
        }

        $token = auth()->user()->createToken( 'authToken' )->plainTextToken;

        return response()->json( compact( 'token' ) );
    }
}
