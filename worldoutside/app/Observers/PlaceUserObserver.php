<?php

namespace App\Observers;

use App\Models\PlaceUser;
use App\Services\BadgeService;

class PlaceUserObserver
{
    public function saved(PlaceUser $placeUser)
    {
        // Only check badges if visit is verified
        if ($placeUser->visit_type === 'verified') {
            $user = $placeUser->user;
            if ($user) {
                app(BadgeService::class)->checkAndAwardBadges($user);
            }
        }
    }
}
