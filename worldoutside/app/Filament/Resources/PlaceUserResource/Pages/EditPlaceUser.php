<?php

namespace App\Filament\Resources\PlaceUserResource\Pages;


use App\Filament\Resources\PlaceUserResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Notifications\Notification;

class EditPlaceUser extends EditRecord
{
    protected static string $resource = PlaceUserResource::class;

    protected function getHeaderActions(): array
    {
        $actions = [
            Actions\DeleteAction::make(),
        ];

        $record = $this->record;
        if ($record && $record->visit_type === 'pending') {
            $actions[] = Actions\Action::make('approve')
                ->label('Schváliť')
                ->color('success')
                ->action(function () use ($record) {
                    $record->visit_type = 'verified';
                    $record->save();
                    // Send notification
                    $user = $record->user;
                    if ($user) {
                        $user->notify(new \App\Notifications\VisitStatusChanged($record->place, 'approved'));
                    }
                    $this->refreshFormData(['visit_type']);
                    Notification::make()
                        ->success()
                        ->title('Návšteva bola schválená.')
                        ->send();
                });
            $actions[] = Actions\Action::make('reject')
                ->label('Zamietnuť')
                ->color('danger')
                ->action(function () use ($record) {
                    $record->visit_type = 'soft';
                    $record->save();
                    // Send notification
                    $user = $record->user;
                    if ($user) {
                        $user->notify(new \App\Notifications\VisitStatusChanged($record->place, 'rejected'));
                    }
                    $this->refreshFormData(['visit_type']);
                    Notification::make()
                        ->success()
                        ->title('Návšteva bola zamietnutá.')
                        ->send();
                });
        }
        return $actions;
    }
}
