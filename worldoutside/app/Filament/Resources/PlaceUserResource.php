<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PlaceUserResource\Pages;
use App\Filament\Resources\PlaceUserResource\RelationManagers;
use App\Models\PlaceUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PlaceUserResource extends Resource
{
    protected static ?string $model = PlaceUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('user_id')
                    ->relationship('user', 'name')
                    ->required(),
                Forms\Components\Select::make('place_id')
                    ->relationship('place', 'name')
                    ->required(),
                Forms\Components\Select::make('visit_type')
                    ->options([
                        'soft' => 'Soft',
                        'verified' => 'Verified',
                        'pending' => 'Pending',
                    ])
                    ->required(),
                Forms\Components\ViewField::make('photo')
                    ->label('Photo')
                    ->view('filament.components.image-preview')
                    ->disabled(),
                Forms\Components\Textarea::make('exif')
                    ->label('EXIF Data')
                    ->rows(8)
                    ->formatStateUsing(function ($state) {
                        if (empty($state)) return '';
                        if (is_string($state)) {
                            $decoded = json_decode($state, true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                                return json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                            }
                            return $state;
                        }
                        if (is_array($state) || is_object($state)) {
                            return json_encode($state, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
                        }
                        return (string) $state;
                    })
                    ->disabled(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('visit_type', 'asc')
            ->columns([
                Tables\Columns\TextColumn::make('id')->sortable(),
                Tables\Columns\TextColumn::make('user.name')->label('User')->searchable(),
                Tables\Columns\TextColumn::make('place.name')->label('Place')->searchable(),
                Tables\Columns\BadgeColumn::make('visit_type')
                    ->label('Visit Type')
                    ->sortable()
                    ->colors([
                        'soft' => 'yellow',
                        'verified' => 'green',
                        'pending' => 'gray',
                    ]),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('visit_type')
                    ->label('Typ overenia')
                    ->options([
                        'soft' => 'Soft',
                        'verified' => 'Verified',
                        'pending' => 'Pending',
                    ]),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('approve')
                    ->label('Schváliť')
                    ->visible(fn($record) => $record->visit_type === 'pending')
                    ->action(function ($record) {
                        $record->visit_type = 'verified';
                        $record->save();
                    })
                    ->color('green'),
                Tables\Actions\Action::make('reject')
                    ->label('Zamietnuť')
                    ->visible(fn($record) => $record->visit_type === 'pending')
                    ->action(function ($record) {
                        $record->visit_type = 'soft';
                        $record->save();
                    })
                    ->color('red'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlaceUsers::route('/'),
            'create' => Pages\CreatePlaceUser::route('/create'),
            'edit' => Pages\EditPlaceUser::route('/{record}/edit'),
        ];
    }
}
