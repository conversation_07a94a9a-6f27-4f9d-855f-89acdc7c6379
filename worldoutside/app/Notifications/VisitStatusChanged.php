<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use App\Models\Place;

class VisitStatusChanged extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public Place $place,
        public string $status // 'approved' or 'rejected'
    ) {}

    public function via($notifiable)
    {
        return ['mail'];
    }

    public function toMail($notifiable)
    {
        $statusText = $this->status === 'approved' ? 'schválená' : 'zamietnutá';
        $subject = "Návšteva miesta '{$this->place->name}' bola $statusText";
        $line = $this->status === 'approved'
            ? '<PERSON><PERSON><PERSON> návšteva bola schválená. Gratulujeme!'
            : '<PERSON><PERSON><PERSON> návšteva bola zamietnutá. Skúste to znova alebo kontaktujte administrátora.';

        return (new MailMessage)
            ->subject($subject)
            ->greeting('Dobrý deň!')
            ->line("Návšteva miesta '{$this->place->name}' bola $statusText.")
            ->line($line);
    }
}
