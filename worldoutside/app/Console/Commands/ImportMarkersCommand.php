<?php

namespace App\Console\Commands;

use App\Models\Place;
use Illuminate\Console\Command;
use JsonException;

class ImportMarkersCommand extends Command {
    protected $signature = 'import:markers {filename}';

    protected $description = 'This command imports markers from a geojson data.';

    /**
     * @throws JsonException
     */
    public function handle(): void {
        if ($this->argument('filename') === 'all') {
            $this->import('mountains');
            return;
        }

        $this->import($this->argument('filename'));
    }

    /**
     * @throws JsonException
     */
    private function import(string $filename): void {
        $path = app_path("ImportData" . DIRECTORY_SEPARATOR . $filename . ".geojson");
        $data = json_decode( file_get_contents( $path ), true, 512, JSON_THROW_ON_ERROR );

        if (empty($data) || $data['type'] !== 'FeatureCollection') {
            $this->error('Invalid geojson data.');
            return;
        }

        foreach ($data['features'] as $feature) {
            if ($feature['type'] !== 'Feature') {
                continue;
            }

            $properties = $feature['properties'];
            $geometry = $feature['geometry'];
            $id = $feature['id'];

            if (!array_key_exists('name', $properties)) {
                continue;
            }

            $place = Place::firstOrNew( [ 'feature_id' => $id ] );

            $place->feature_id = $id;
            $place->name = $properties['name'];
            $place->description = $properties['wikipedia'] ?? '';
            $place->latitude = $geometry['coordinates'][1];
            $place->longitude = $geometry['coordinates'][0];
            $place->image = $properties['image'] ?? '';
            // Ensure elevation is integer if present and numeric
            if (isset($properties['ele']) && is_numeric($properties['ele'])) {
                $place->elevation = (int) round($properties['ele']);
            } else {
                $place->elevation = null;
            }

            $place->save();
        }

        $this->info('Markers imported successfully.');
    }
}
