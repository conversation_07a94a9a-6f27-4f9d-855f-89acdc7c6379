<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlaceUser extends Model {
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function place()
    {
        return $this->belongsTo(Place::class);
    }
    use HasFactory;

    protected $table = 'place_user';

    protected $fillable = [
        'user_id',
        'place_id',
        'visit_type',
        'photo',
        'exif',
    ];

    protected $casts = [
        'exif' => 'array',
    ];
}
