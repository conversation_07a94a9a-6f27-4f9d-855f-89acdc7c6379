<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Models\PlaceUser;
use App\Observers\PlaceUserObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        PlaceUser::observe(PlaceUserObserver::class);
    }
}
