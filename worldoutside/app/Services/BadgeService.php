<?php

namespace App\Services;

use App\Models\User;
use App\Models\Badge;
use App\Models\Place;
use Illuminate\Support\Carbon;

class BadgeService
{
    /**
     * Check and award badges for a user after a new visit.
     */
    public function checkAndAwardBadges(User $user): array
    {
        $awarded = [];
        $visitedPlaces = $user->places()->wherePivot('visit_type', 'verified')->get();
        $visitedCount = $visitedPlaces->count();

        // Badge: First 5 peaks
        $badge5 = Badge::where('slug', 'first-5-peaks')->first();
        if ($badge5 && $visitedCount >= 5 && !$user->badges->contains($badge5->id)) {
            $user->badges()->attach($badge5->id, ['awarded_at' => Carbon::now()]);
            $awarded[] = $badge5;
        }

        // Badge: First 10 peaks
        $badge10 = Badge::where('slug', 'first-10-peaks')->first();
        if ($badge10 && $visitedCount >= 10 && !$user->badges->contains($badge10->id)) {
            $user->badges()->attach($badge10->id, ['awarded_at' => Carbon::now()]);
            $awarded[] = $badge10;
        }

        // Badge: First 2000m peak
        $badge2000 = Badge::where('slug', 'first-2000m-peak')->first();
        if ($badge2000 && !$user->badges->contains($badge2000->id)) {
            $has2000 = $visitedPlaces->contains(function($place) {
                return $place->elevation >= 2000;
            });
            if ($has2000) {
                $user->badges()->attach($badge2000->id, ['awarded_at' => Carbon::now()]);
                $awarded[] = $badge2000;
            }
        }

        return $awarded;
    }
}
