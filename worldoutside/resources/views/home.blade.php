<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <title>World Outside</title>

        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="" />
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin=""></script>
    </head>

    <body>

    @php($id = 0)
    @foreach($places as $place)
        <h1>{{ $place->name }}</h1>
        <p>{{ $place->description }}</p>
        <div id="id-<?php echo $id; ?>" style="height: 300px; width: 300px;"></div>

        <script>
            var map = L.map('<?php echo 'id-' . $id; ?>').setView([<?php echo $place->latitude; ?>, <?php echo
    $place->longitude; ?>], 13);

            L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 19,
                attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
            }).addTo(map);
        </script>
        @php($id++)
    @endforeach



    </body>

</html>
