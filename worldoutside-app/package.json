{"name": "worldoutside-app", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@mantine/core": "^7.11.2", "@mantine/form": "^7.11.2", "@mantine/hooks": "^7.11.2", "@mantine/notifications": "^7.11.2", "@tabler/icons-react": "3.31.0", "@tanstack/react-query": "^5.51.18", "@types/node": "^20.14.11", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "exif-js": "^2.3.0", "idb-keyval": "^6.2.1", "leaflet": "^1.9.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.51.3", "react-leaflet": "^4.2.1", "react-router": "^7.6.0", "react-router-dom": "^7.6.0", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "workbox-webpack-plugin": "^7.0.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.2.1", "@types/leaflet": "^1.9.12", "postcss": "^8.4.39", "postcss-preset-mantine": "^1.17.0", "postcss-simple-vars": "^7.0.1", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.6"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "prettier": {"plugins": ["prettier-plugin-tailwindcss"], "parser": "typescript"}, "postcss": {"plugins": {"postcss-preset-mantine": {}}}}