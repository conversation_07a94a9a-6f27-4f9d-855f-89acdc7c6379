<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="AUTODETECT_INDENTS" value="false" />
    <option name="WRAP_WHEN_TYPING_REACHES_RIGHT_MARGIN" value="true" />
    <option name="SOFT_MARGINS" value="120" />
    <HTMLCodeStyleSettings>
      <option name="HTML_SPACE_INSIDE_EMPTY_TAG" value="true" />
    </HTMLCodeStyleSettings>
    <JSCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="Remove" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </JSCodeStyleSettings>
    <PHPCodeStyleSettings>
      <option name="ALIGN_KEY_VALUE_PAIRS" value="true" />
      <option name="ALIGN_ASSIGNMENTS" value="true" />
      <option name="PHPDOC_BLANK_LINES_AROUND_PARAMETERS" value="true" />
      <option name="PHPDOC_PARAM_SPACES_BETWEEN_TAG_AND_TYPE" value="2" />
      <option name="PHPDOC_PARAM_SPACES_BETWEEN_TYPE_AND_NAME" value="2" />
      <option name="PHPDOC_PARAM_SPACES_BETWEEN_NAME_AND_DESCRIPTION" value="2" />
      <option name="ANONYMOUS_BRACE_STYLE" value="2" />
      <option name="LOWER_CASE_BOOLEAN_CONST" value="true" />
      <option name="LOWER_CASE_NULL_CONST" value="true" />
      <option name="ELSE_IF_STYLE" value="COMBINE" />
      <option name="GETTERS_SETTERS_NAMING_STYLE" value="SNAKE_CASE" />
      <option name="VARIABLE_NAMING_STYLE" value="SNAKE_CASE" />
      <option name="BLANK_LINES_BEFORE_RETURN_STATEMENT" value="1" />
      <option name="KEEP_RPAREN_AND_LBRACE_ON_ONE_LINE" value="true" />
      <option name="SPACES_AROUND_VAR_WITHIN_BRACKETS" value="true" />
      <option name="SPACE_BEFORE_UNARY_NOT" value="true" />
      <option name="SPACE_AFTER_UNARY_NOT" value="true" />
      <option name="NAMESPACE_BRACE_STYLE" value="2" />
    </PHPCodeStyleSettings>
    <TypeScriptCodeStyleSettings version="0">
      <option name="FORCE_SEMICOLON_STYLE" value="true" />
      <option name="SPACE_BEFORE_FUNCTION_LEFT_PARENTH" value="false" />
      <option name="FORCE_QUOTE_STYlE" value="true" />
      <option name="ENFORCE_TRAILING_COMMA" value="Remove" />
      <option name="SPACES_WITHIN_OBJECT_LITERAL_BRACES" value="true" />
      <option name="SPACES_WITHIN_IMPORTS" value="true" />
    </TypeScriptCodeStyleSettings>
    <codeStyleSettings language="HTML">
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="JavaScript">
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="PHP">
      <option name="KEEP_CONTROL_STATEMENT_IN_ONE_LINE" value="false" />
      <option name="BLANK_LINES_AFTER_PACKAGE" value="1" />
      <option name="CLASS_BRACE_STYLE" value="1" />
      <option name="METHOD_BRACE_STYLE" value="1" />
      <option name="SPECIAL_ELSE_IF_TREATMENT" value="true" />
      <option name="ALIGN_MULTILINE_CHAINED_METHODS" value="true" />
      <option name="ALIGN_MULTILINE_PARAMETERS" value="false" />
      <option name="ALIGN_MULTILINE_FOR" value="false" />
      <option name="ALIGN_MULTILINE_BINARY_OPERATION" value="true" />
      <option name="SPACE_AROUND_UNARY_OPERATOR" value="true" />
      <option name="SPACE_WITHIN_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_METHOD_CALL_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_METHOD_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_IF_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_WHILE_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_FOR_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_CATCH_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_SWITCH_PARENTHESES" value="true" />
      <option name="SPACE_WITHIN_ARRAY_INITIALIZER_BRACES" value="true" />
      <option name="SPACE_AFTER_TYPE_CAST" value="true" />
      <option name="CALL_PARAMETERS_WRAP" value="1" />
      <option name="METHOD_PARAMETERS_WRAP" value="5" />
      <option name="METHOD_PARAMETERS_LPAREN_ON_NEXT_LINE" value="true" />
      <option name="METHOD_PARAMETERS_RPAREN_ON_NEXT_LINE" value="true" />
      <option name="PARENTHESES_EXPRESSION_LPAREN_WRAP" value="true" />
      <option name="PARENTHESES_EXPRESSION_RPAREN_WRAP" value="true" />
      <option name="FOR_STATEMENT_LPAREN_ON_NEXT_LINE" value="true" />
      <option name="FOR_STATEMENT_RPAREN_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_WRAP" value="5" />
      <option name="ARRAY_INITIALIZER_LBRACE_ON_NEXT_LINE" value="true" />
      <option name="ARRAY_INITIALIZER_RBRACE_ON_NEXT_LINE" value="true" />
      <option name="IF_BRACE_FORCE" value="3" />
      <option name="DOWHILE_BRACE_FORCE" value="3" />
      <option name="WHILE_BRACE_FORCE" value="3" />
      <option name="FOR_BRACE_FORCE" value="3" />
      <indentOptions>
        <option name="USE_TAB_CHARACTER" value="true" />
        <option name="SMART_TABS" value="true" />
      </indentOptions>
    </codeStyleSettings>
    <codeStyleSettings language="TypeScript">
      <option name="SOFT_MARGINS" value="80" />
      <indentOptions>
        <option name="INDENT_SIZE" value="2" />
        <option name="CONTINUATION_INDENT_SIZE" value="2" />
        <option name="TAB_SIZE" value="2" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>