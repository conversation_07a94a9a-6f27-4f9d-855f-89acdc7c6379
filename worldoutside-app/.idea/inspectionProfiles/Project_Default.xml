<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="ComparisonScalarOrderInspection" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="CssRedundantUnit" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="CucumberExamplesColon" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CucumberMissedExamples" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CucumberTableInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="CucumberUndefinedStep" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="ForgottenDebugOutputInspection" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="configuration">
        <list>
          <option value="\Codeception\Util\Debug::debug" />
          <option value="\Codeception\Util\Debug::pause" />
          <option value="\Doctrine\Common\Util\Debug::dump" />
          <option value="\Doctrine\Common\Util\Debug::export" />
          <option value="\Illuminate\Support\Debug\Dumper::dump" />
          <option value="\Symfony\Component\Debug\Debug::enable" />
          <option value="\Symfony\Component\Debug\DebugClassLoader::enable" />
          <option value="\Symfony\Component\Debug\ErrorHandler::register" />
          <option value="\Symfony\Component\Debug\ExceptionHandler::register" />
          <option value="\TYPO3\CMS\Core\Utility\DebugUtility::debug" />
          <option value="\Zend\Debug\Debug::dump" />
          <option value="\Zend\Di\Display\Console::export" />
          <option value="dd" />
          <option value="debug_print_backtrace" />
          <option value="debug_zval_dump" />
          <option value="dpm" />
          <option value="dpq" />
          <option value="dsm" />
          <option value="dump" />
          <option value="dvm" />
          <option value="error_log" />
          <option value="kpr" />
          <option value="phpinfo" />
          <option value="print_r" />
          <option value="var_dump" />
          <option value="var_export" />
          <option value="wp_die" />
          <option value="xdebug_break" />
          <option value="xdebug_call_class" />
          <option value="xdebug_call_file" />
          <option value="xdebug_call_function" />
          <option value="xdebug_call_line" />
          <option value="xdebug_code_coverage_started" />
          <option value="xdebug_debug_zval" />
          <option value="xdebug_debug_zval_stdout" />
          <option value="xdebug_dump_superglobals" />
          <option value="xdebug_enable" />
          <option value="xdebug_get_code_coverage" />
          <option value="xdebug_get_collected_errors" />
          <option value="xdebug_get_declared_vars" />
          <option value="xdebug_get_function_stack" />
          <option value="xdebug_get_headers" />
          <option value="xdebug_get_monitored_functions" />
          <option value="xdebug_get_profiler_filename" />
          <option value="xdebug_get_stack_depth" />
          <option value="xdebug_get_tracefile_name" />
          <option value="xdebug_is_enabled" />
          <option value="xdebug_memory_usage" />
          <option value="xdebug_peak_memory_usage" />
          <option value="xdebug_print_function_stack" />
          <option value="xdebug_start_code_coverage" />
          <option value="xdebug_start_error_collection" />
          <option value="xdebug_start_function_monitor" />
          <option value="xdebug_start_trace" />
          <option value="xdebug_stop_code_coverage" />
          <option value="xdebug_stop_error_collection" />
          <option value="xdebug_stop_function_monitor" />
          <option value="xdebug_stop_trace" />
          <option value="xdebug_time_index" />
          <option value="xdebug_var_dump" />
        </list>
      </option>
      <option name="migratedIntoUserSpace" value="true" />
    </inspection_tool>
    <inspection_tool class="GherkinBrokenTableInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GherkinMisplacedBackground" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GherkinScenarioToScenarioOutline" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrazieInspection" enabled="false" level="GRAMMAR_ERROR" enabled_by_default="false" />
    <inspection_tool class="JsCoverageInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="LessResolvedByNameOnly" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="LessUnresolvedMixin" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="LessUnresolvedVariable" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MessDetectorValidationInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="MongoJSDeprecationInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MongoJSExtDeprecationInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MongoJSExtResolveInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MongoJSExtSideEffectsInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MongoJSResolveInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="MongoJSSideEffectsInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="OraMissingBodyInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="OraOverloadInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="OraUnmatchedForwardDeclarationInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="PhingDomInspection" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="PhpCSValidationInspection" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="PhpCoverageInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PhpUnused" enabled="false" level="WEAK WARNING" enabled_by_default="false" />
    <inspection_tool class="SecurityAdvisoriesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="optionConfiguration">
        <list>
          <option value="barryvdh/laravel-debugbar" />
          <option value="behat/behat" />
          <option value="brianium/paratest" />
          <option value="codeception/codeception" />
          <option value="codedungeon/phpunit-result-printer" />
          <option value="composer/composer" />
          <option value="doctrine/coding-standard" />
          <option value="filp/whoops" />
          <option value="friendsofphp/php-cs-fixer" />
          <option value="humbug/humbug" />
          <option value="infection/infection" />
          <option value="jakub-onderka/php-parallel-lint" />
          <option value="johnkary/phpunit-speedtrap" />
          <option value="kalessil/production-dependencies-guard" />
          <option value="mikey179/vfsStream" />
          <option value="mockery/mockery" />
          <option value="mybuilder/phpunit-accelerator" />
          <option value="orchestra/testbench" />
          <option value="pdepend/pdepend" />
          <option value="phan/phan" />
          <option value="phing/phing" />
          <option value="phpcompatibility/php-compatibility" />
          <option value="phpmd/phpmd" />
          <option value="phpro/grumphp" />
          <option value="phpspec/phpspec" />
          <option value="phpspec/prophecy" />
          <option value="phpstan/phpstan" />
          <option value="phpunit/phpunit" />
          <option value="povils/phpmnd" />
          <option value="roave/security-advisories" />
          <option value="satooshi/php-coveralls" />
          <option value="sebastian/phpcpd" />
          <option value="slevomat/coding-standard" />
          <option value="spatie/phpunit-watcher" />
          <option value="squizlabs/php_codesniffer" />
          <option value="sstalle/php7cc" />
          <option value="symfony/debug" />
          <option value="symfony/maker-bundle" />
          <option value="symfony/phpunit-bridge" />
          <option value="symfony/var-dumper" />
          <option value="vimeo/psalm" />
          <option value="wimg/php-compatibility" />
          <option value="wp-coding-standards/wpcs" />
          <option value="yiisoft/yii2-coding-standards" />
          <option value="yiisoft/yii2-debug" />
          <option value="yiisoft/yii2-gii" />
          <option value="zendframework/zend-coding-standard" />
          <option value="zendframework/zend-debug" />
          <option value="zendframework/zend-test" />
        </list>
      </option>
    </inspection_tool>
    <inspection_tool class="SpellCheckingInspection" enabled="false" level="TYPO" enabled_by_default="false">
      <option name="processCode" value="true" />
      <option name="processLiterals" value="true" />
      <option name="processComments" value="true" />
    </inspection_tool>
  </profile>
</component>