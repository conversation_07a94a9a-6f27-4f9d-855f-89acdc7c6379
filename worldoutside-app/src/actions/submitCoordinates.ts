export async function submitCoordinates(placeId: number, latitude: number, longitude: number): Promise<any> {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/places/submit-coordinates`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
    },
    body: JSON.stringify({
      place_id: placeId,
      latitude,
      longitude,
    }),
  });

  if (!response.ok) {
    throw new Error("Nepodarilo sa overiť návštevu");
  }

  return response.json();
}
