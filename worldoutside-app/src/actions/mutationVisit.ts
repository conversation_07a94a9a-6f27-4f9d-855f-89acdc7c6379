import { panic, success } from "../misc/notif";

export function mutationVisit(id: number) {
  return fetch(`${process.env.REACT_APP_API_URL}/places/${id}/visit`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      "Content-Type": "application/json",
    },
  })
    .then(async (response) => {
      if (response.status === 400) {
        throw new Error("Pre označenie miesta sa musíte prihlásiť");
      }

      if (!response.ok) {
        // Try to parse backend error message
        let msg = "Nepodarilo sa získať dáta";
        try {
          const data = await response.json();
          if (data && data.message === "Place already visited") {
            msg = "Toto miesto ste už označili ako navštívené.";
          } else if (data && data.message) {
            msg = data.message;
          }
        } catch {}
        throw new Error(msg);
      }

      success({
        title: "<PERSON><PERSON>o ozna<PERSON>en<PERSON>",
        message: "<PERSON><PERSON><PERSON> bolo ú<PERSON>ešne označené",
      });

      return response.json();
    })
    .catch((error) => {
      panic({
        title: "Nepodarilo sa označiť miesto",
        message: error.message,
      });
    });
}
