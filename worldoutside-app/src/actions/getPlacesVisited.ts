import { queryOptions } from "@tanstack/react-query";

export function getPlacesVisitedOptions() {
  return queryOptions({
    queryKey: ["places/visited"],
    queryFn: getPlacesVisited,
  });
}

async function getPlacesVisited(): Promise<Place[]> {
  const response = await fetch(
    `${process.env.REACT_APP_API_URL}/places/visited`,
    {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      },
    },
  );

  if (response.status === 401) {
    throw new Error("Pre zobrazenie navštívených miest sa musíte");
  }

  if (!response.ok) {
    throw new Error("Nepodarilo sa získať dáta");
  }

  const data = (await response.json()) as { data: Place[] };

  return data.data;
}
