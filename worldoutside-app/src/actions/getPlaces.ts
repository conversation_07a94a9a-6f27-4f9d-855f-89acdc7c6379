import { QueryFunctionContext, queryOptions } from "@tanstack/react-query";

export function getPlacesOptions(latitude: number, longitude: number) {
  return queryOptions({
    queryKey: ["places", latitude, longitude],
    queryFn: getPlaces,
  });
}

async function getPlaces({
  queryKey,
}: QueryFunctionContext<[string, number, number]>): Promise<Place[]> {
  const [_, latitude, longitude] = queryKey;

  const response = await fetch(
    `${process.env.REACT_APP_API_URL}/places/${latitude}/${longitude}`,
    {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      },
    },
  );

  if (!response.ok) {
    throw new Error("Nepodarilo sa získať dáta");
  }

  const data = (await response.json()) as { data: Place[] };

  return data.data;
}
