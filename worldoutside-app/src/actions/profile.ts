import { queryOptions } from "@tanstack/react-query";

export function getProfileOptions() {
  return queryOptions({
    queryKey: ["profile"],
    queryFn: getProfile,
  });
}

async function getProfile() {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/profile`, {
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
    },
  });
  if (!response.ok) throw new Error("Nepodarilo sa načítať profil");
  return response.json();
}

export async function updateProfile(formData: FormData) {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/profile`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
    },
    body: formData,
  });
  if (!response.ok) throw new Error("Nepodarilo sa uložiť profil");
  return response.json();
}
