import { queryOptions } from "@tanstack/react-query";

export function getPlacesPlannedOptions() {
  return queryOptions({
    queryKey: ["places/planned"],
    queryFn: getPlacesPlanned,
    staleTime: 0,
  });
}

async function getPlacesPlanned() {
  const response = await fetch(
    `${process.env.REACT_APP_API_URL}/places/planned`,
    {
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      },
    },
  );
  if (!response.ok) throw new Error("Nepodarilo sa získať plánované miesta");
  const data = (await response.json()) as { data: Place[] };
  return data.data;
}

export async function addPlannedPlace(id: number) {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/places/${id}/plan`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      "Content-Type": "application/json",
    },
  });
  if (!response.ok) throw new Error("Nepodarilo sa pridať do plánovaných");
  return response.json();
}

export async function removePlannedPlace(id: number) {
  const response = await fetch(`${process.env.REACT_APP_API_URL}/places/${id}/unplan`, {
    method: "POST",
    headers: {
      Accept: "application/json",
      Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
      "Content-Type": "application/json",
    },
  });
  if (!response.ok) throw new Error("Nepodarilo sa odstrániť z plánovaných");
  return response.json();
}
