
import React, { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { Card, Stack, Title, Text, Group, Loader, Center, Alert, Badge, Avatar, Textarea, FileInput, Button } from "@mantine/core";
import { getProfileStats } from "../actions/getProfileStats";
import { getProfileBadges } from "../actions/getProfileBadges";
import { getProfileOptions, updateProfile } from "../actions/profile";

export default function Profile() {
  const { data: stats, isPending: statsPending, error: statsError } = useQuery({
    queryKey: ["profile/stats"],
    queryFn: getProfileStats,
  });
  const { data: badgesData, isPending: badgesPending, error: badgesError } = useQuery({
    queryKey: ["profile/badges"],
    queryFn: getProfileBadges,
  });
  const { data: profile, isPending: profilePending, error: profileError, refetch: refetchProfile } = useQuery(getProfileOptions());
  // Helper pro asset URL (obr<PERSON>zky)
  function getAssetUrl(path: string) {
    const assetUrl = process.env.REACT_APP_ASSET_URL || (process.env.REACT_APP_API_URL ? process.env.REACT_APP_API_URL.replace(/\/api$/, '') : '');
    return `${assetUrl}/storage/${path}`;
  }

  const mutation = useMutation({
    mutationFn: updateProfile,
    onSuccess: (data) => {
      refetchProfile();
      if (data && data.profile_image) {
        setPreview(getAssetUrl(data.profile_image));
        setFile(null);
      }
    },
  });

  const [bio, setBio] = useState("");
  const [file, setFile] = useState<File | null>(null);
  const [preview, setPreview] = useState<string | null>(null);

  // Set initial bio and preview when profile loads
  React.useEffect(() => {
    if (profile) {
      setBio(profile.bio || "");
      setPreview(profile.profile_image ? getAssetUrl(profile.profile_image) : null);
    }
  }, [profile]);

  function handleFileChange(f: File | null) {
    setFile(f);
    if (f) {
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target?.result as string);
      reader.readAsDataURL(f);
    } else {
      setPreview(profile?.profile_image ? getAssetUrl(profile.profile_image) : null);
    }
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    const formData = new FormData();
    if (bio) formData.append("bio", bio);
    if (file) formData.append("profile_image", file);
    mutation.mutate(formData);
  }

  if (statsPending || badgesPending || profilePending) {
    return (
      <Center style={{ minHeight: 200 }}>
        <Loader />
      </Center>
    );
  }

  return (
    <Stack align="center" gap="md" style={{ maxWidth: 420, margin: "0 auto", width: "100%" }}>
      <Title order={2} mt="md" mb="md">Môj profil</Title>

      {statsError && (
        <Alert color="red" title="Chyba" w="100%">{statsError.message}</Alert>
      )}
      {badgesError && (
        <Alert color="red" title="Chyba" w="100%">{badgesError.message}</Alert>
      )}
      {profileError && (
        <Alert color="red" title="Chyba" w="100%">{profileError.message}</Alert>
      )}

      <form onSubmit={handleSubmit} style={{ width: "100%" }}>
        <Stack gap="sm" align="center">
          <Avatar src={preview} size={96} radius={48} mb={4} />
          <FileInput
            label="Profilový obrázok"
            accept="image/*"
            value={file}
            onChange={handleFileChange}
            clearable
            style={{ width: "100%" }}
          />
          <Textarea
            label="Bio / Popis používateľa"
            value={bio}
            onChange={(e) => setBio(e.currentTarget.value)}
            maxLength={500}
            minRows={3}
            style={{ width: "100%" }}
          />
          <Button type="submit" loading={mutation.isPending} style={{ width: "100%" }}>
            Uložiť profil
          </Button>
        </Stack>
      </form>

      {stats && (
        <Card shadow="xs" padding="md" radius="md" withBorder w="100%">
          <Group justify="space-between">
            <Text>Počet vrcholov:</Text>
            <Text fw={700}>{stats.peaks_count}</Text>
          </Group>
          <Group justify="space-between">
            <Text>Celková nastúpaná výška:</Text>
            <Text fw={700}>{stats.total_elevation} m</Text>
          </Group>
        </Card>
      )}

      <Title order={4} mt="md">Získané odznaky</Title>
      <Group gap="sm" w="100%" wrap="wrap">
        {badgesData && badgesData.badges.length > 0 ? (
          badgesData.badges.map((badge: any) => (
            <Badge key={badge.id} size="lg" color="teal" leftSection={badge.icon}>
              {badge.name}
            </Badge>
          ))
        ) : (
          <Text c="dimmed">Zatiaľ žiadne odznaky</Text>
        )}
      </Group>
    </Stack>
  );
}
