import { NavLink, Card, Stack, Title, Text, Center, Loader, Alert } from "@mantine/core";
import { Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getPlacesVisitedOptions } from "../actions/getPlacesVisited";

export default function Visited() {
  const { data, isPending, error } = useQuery(getPlacesVisitedOptions());

  if (isPending) {
    return (
      <Center style={{ minHeight: 200 }}>
        <Loader />
      </Center>
    );
  }

  return (
    <Stack align="center" gap="md" style={{ maxWidth: 420, margin: "0 auto", width: "100%" }}>
      <Title order={2} mt="md" mb="md">Navštívené miesta</Title>

      {error && (
        <Alert color="red" title="Chyba" w="100%">
          {error.message} <Link to="/prihlasenie">prihlásiť</Link>
        </Alert>
      )}

      {data && data.length > 0 ? (
        <Stack w="100%" gap="sm">
          {data.map((place: any) => (
            <Card key={place.id} shadow="xs" padding="md" radius="md" withBorder>
              <NavLink
                label={place.name}
                href={`https://mapy.cz/zakladni?x=${place.longitude}&y=${place.latitude}&z=15`}
                target="_blank"
                rel="noopener noreferrer"
                description={<Text size="sm" c="dimmed">{place.description}</Text>}
              />
            </Card>
          ))}
        </Stack>
      ) : (
        <Text c="dimmed">Žiadne navštívené miesta</Text>
      )}
    </Stack>
  );
}
