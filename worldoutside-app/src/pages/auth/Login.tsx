import { useForm } from "@mantine/form";
import { Button, PasswordInput, TextInput, Paper, Title, Text, Stack, Center, Group } from "@mantine/core";
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import logged from "../../misc/logged";

export default function Login() {
  const navigate = useNavigate();

  useEffect(() => {
    if (logged()) {
      navigate("/");
    }
  }, [navigate]);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },

    validate: {
      email: (value) => {
        if (!value) {
          return "Email je povinný";
        }

        if (!value.includes("@")) {
          return "Email musí obsahovať @";
        }
      },
      password: (value) => {
        if (!value) {
          return "Heslo je povinné";
        }
      },
    },
  });

  const handleLogin = (values: any) => {
    fetch(`${process.env.REACT_APP_API_URL}/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(values),
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Nepodarilo sa prihlásiť");
        }

        return response.json();
      })
      .then((data) => {
        localStorage.setItem("wo-token", data.token);
        window.location.href = "/";
      })
      .catch((error) => {
        console.error(error);
      });
  };

  return (
    <Center style={{ minHeight: "100vh" }}>
      <Paper shadow="md" radius="md" p="xl" withBorder style={{ minWidth: 340 }}>
        <Stack>
          <Title order={2} style={{ textAlign: "center", marginBottom: "var(--mantine-spacing-md)" }}>
            Prihlásenie
          </Title>
          <form onSubmit={form.onSubmit(handleLogin)}>
            <Stack>
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                key={form.key("email")}
                {...form.getInputProps("email")}
                required
              />
              <PasswordInput
                label="Heslo"
                placeholder="********"
                key={form.key("password")}
                {...form.getInputProps("password")}
                required
              />
              <Group justify="flex-end" mt="md">
                <Button type="submit" fullWidth>
                  Prihlásiť sa
                </Button>
              </Group>
            </Stack>
          </form>
        </Stack>
      </Paper>
    </Center>
  );
}
