
import { Card, Stack, Title, Text, Group, Button } from "@mantine/core";
import { useQuery, useMutation } from "@tanstack/react-query";
import { getProfileStats } from "../actions/getProfileStats";
import { getPlacesVisitedOptions } from "../actions/getPlacesVisited";
import { getPlacesPlannedOptions, removePlannedPlace } from "../actions/getPlacesPlanned";

export default function Dashboard() {
  const { data: stats, isPending: statsPending, error: statsError } = useQuery({
    queryKey: ["profile/stats"],
    queryFn: getProfileStats,
  });
  const { data: visited, isPending: visitedPending, error: visitedError } = useQuery(getPlacesVisitedOptions());
  const { data: planned, isPending: plannedPending, refetch: refetchPlanned } = useQuery(getPlacesPlannedOptions());
  const removeMutation = useMutation({
    mutationFn: removePlannedPlace,
    onSuccess: () => refetchPlanned(),
  });

  if (statsPending || visitedPending || plannedPending) {
    return <div>Načítavam...</div>;
  }

  return (
    <Stack gap="md" style={{ maxWidth: 900, margin: "0 auto", width: "100%" }}>
      <Title order={2} mt="md" mb="md">Dashboard</Title>
      <Group gap="md" style={{ display: 'flex', flexWrap: 'wrap' }}>
        <Card shadow="xs" padding="md" radius="md" withBorder style={{ minWidth: 220, flex: 1 }}>
          <Text size="lg" fw={600}>Navštívené vrcholy</Text>
          <Text size="xl" fw={700}>{stats?.peaks_count ?? 0}</Text>
        </Card>
        <Card shadow="xs" padding="md" radius="md" withBorder style={{ minWidth: 220, flex: 1 }}>
          <Text size="lg" fw={600}>Celková nastúpaná výška</Text>
          <Text size="xl" fw={700}>{stats?.total_elevation ?? 0} m</Text>
        </Card>
        <Card shadow="xs" padding="md" radius="md" withBorder style={{ minWidth: 220, flex: 1 }}>
          <Text size="lg" fw={600}>Počet túr</Text>
          <Text size="xl" fw={700}>{visited?.length ?? 0}</Text>
        </Card>
      </Group>

      <Title order={4} mt="md">Plánované vrcholy</Title>
      <Stack gap="sm">
        {planned && planned.length > 0 ? planned.map((place: any) => (
          <Card key={place.id} shadow="xs" padding="md" radius="md" withBorder>
            <Group justify="space-between">
              <Text fw={600}>{place.name}</Text>
              <Button color="red" size="xs" onClick={() => removeMutation.mutate(place.id)} loading={removeMutation.isPending}>
                Odstrániť z plánovaných
              </Button>
            </Group>
            <Text size="sm" c="dimmed">{place.description}</Text>
          </Card>
        )) : <Text c="dimmed">Žiadne plánované vrcholy</Text>}
      </Stack>
    </Stack>
  );
}
