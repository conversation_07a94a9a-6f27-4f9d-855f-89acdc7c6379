import React from "react";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import MainLayout from "./layout/MainLayout";
import Login from "./pages/auth/Login";
import Visited from "./pages/Visited";

function App() {
  return (
    <BrowserRouter>
      <MainLayout>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/prihlasenie" element={<Login />} />
          <Route path="/navstivene" element={<Visited />} />
          <Route path="/profil" element={React.createElement(require("./pages/Profile").default)} />
          <Route path="/dashboard" element={React.createElement(require("./pages/Dashboard").default)} />
          <Route path="*" element={<div>404, Not Found!</div>} />
        </Routes>
      </MainLayout>
    </BrowserRouter>
  );
}

export default App;
