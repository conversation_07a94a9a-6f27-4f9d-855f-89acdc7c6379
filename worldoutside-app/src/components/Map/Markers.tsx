
import { <PERSON><PERSON>, Popup } from "react-leaflet";
import { finishedMarkerIcon, notFinishedMarkerIcon, softVisitedMarkerIcon } from "./MarkerIcons";
import { Button, Badge, Group, Stack, Card, Title, Text, Divider, Avatar, Space } from "@mantine/core";
import { useMutation } from "@tanstack/react-query";
import { addPlannedPlace, removePlannedPlace } from "../../actions/getPlacesPlanned";
import { useState } from "react";
import PhotoVerificationModal from "./PhotoVerificationModal";

export default function Markers({
  data,
  handleVisitedClick,
  handleVerifyClick,
  verifying,
  verifiedIds,
  softVisitedIds,
  plannedIds = [],
  refetchPlanned,
  refetchAll,
}: {
  data: any;
  handleVisitedClick: (id: number) => void;
  handleVerifyClick: (id: number) => void;
  verifying: number | null;
  verifiedIds: number[];
  softVisitedIds: number[];
  plannedIds?: number[];
  refetchPlanned?: () => void;
  refetchAll?: () => void;
}) {
  const addMutation = useMutation({
    mutationFn: addPlannedPlace,
    onSuccess: () => refetchPlanned && refetchPlanned(),
  });
  const removeMutation = useMutation({
    mutationFn: removePlannedPlace,
    onSuccess: () => {
      refetchPlanned && refetchPlanned();
      refetchAll && refetchAll();
    },
  });
  const [photoModalOpen, setPhotoModalOpen] = useState(false);
  const [selectedPlaceId, setSelectedPlaceId] = useState<number | null>(null);

  const handlePhotoVerifyClick = (id: number) => {
    setSelectedPlaceId(id);
    setPhotoModalOpen(true);
  };

  const handlePhotoModalClose = () => {
    setPhotoModalOpen(false);
    setSelectedPlaceId(null);
  };

  const handlePhotoSuccess = () => {
    // Optionally refetch or show notification
  };

  return <>
    {data.map((place: any) => {
    // Prefer backend visit_type if present
    // If place.visit_type is not present, check if it's in softVisitedIds for 'soft' type
    // or use the visited flag for 'verified' type
    const visitType = place.visit_type || 
                     (softVisitedIds.includes(place.id) ? 'soft' : 
                     (place.visited ? 'verified' : null));
    const isPlanned = plannedIds.includes(place.id);

    // Badge priority: verified > soft > pending > planned
    let badge = null;
    if (visitType === 'verified') {
      badge = <Badge color="lime" variant="filled">Overená návšteva</Badge>;
    } else if (visitType === 'soft') {
      badge = <Badge color="yellow" style={{ backgroundColor: '#facc15', color: '#000', border: '1px solid #facc15' }}>Soft návšteva</Badge>;
    } else if (visitType === 'pending') {
      badge = <Badge color="gray">Čaká na overenie</Badge>;
    } else if (isPlanned) {
      badge = <Badge color="blue" variant="light">Plánovaná</Badge>;
    }

    let icon = notFinishedMarkerIcon;
    if (visitType === 'verified') {
      icon = finishedMarkerIcon;
    } else if (visitType === 'soft') {
      icon = softVisitedMarkerIcon;
    }
    // (optionally add more icons for pending in the future)

      return (
      <Marker
        key={place.id}
        position={[parseFloat(place.latitude), parseFloat(place.longitude)]}
        icon={icon}
      >
        <Popup minWidth={340} maxWidth={400}>
          <Card shadow="md" padding="md" radius="md" withBorder={false} style={{ boxShadow: 'none', borderRadius: 16, border: 'none' }}>
            <Group align="flex-start" gap="md" wrap="nowrap">
              <Avatar color="blue" radius="xl" size={48} src={place.image || undefined}>
                {place.name?.[0] || '?'}
              </Avatar>
              <div style={{ flex: 1, minWidth: 0 }}>
                <Title order={5} style={{ marginBottom: 2, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{place.name}</Title>
                <Text size="sm" color="dimmed" lineClamp={3} style={{ wordBreak: 'break-word' }}>{place.description}</Text>
              </div>
            </Group>
            <Divider my="sm" />
            <Group gap={8} style={{ marginBottom: 16, flexWrap: 'wrap' }}>
              {badge}
            </Group>
            <Space h="xs" />
            {(!visitType || visitType === 'planned') && (
              <Button fullWidth size="md" color="yellow" style={{ color: '#000', border: '1px solid #facc15', fontWeight: 600 }} onClick={() => handleVisitedClick(place.id)}>
                Som tu (soft)
              </Button>
            )}
            <Stack gap={8} style={{ marginTop: 8 }}>
              {!isPlanned ? (
                <Button
                  size="xs"
                  color="blue"
                  onClick={() => addMutation.mutate(place.id)}
                  loading={addMutation.isPending}
                  fullWidth
                >
                  Pridať do plánovaných
                </Button>
              ) : (
                <Button
                  size="xs"
                  color="red"
                  onClick={() => removeMutation.mutate(place.id)}
                  loading={removeMutation.isPending}
                  fullWidth
                >
                  Odstrániť z plánovaných
                </Button>
              )}
            </Stack>
            {(visitType === null || visitType === 'soft' || visitType === 'planned') && (
              <Stack gap={8} style={{ marginTop: 16 }}>
                <Button
                  size="md"
                  loading={verifying === place.id}
                  onClick={() => handleVerifyClick(place.id)}
                  color="teal"
                  style={{ fontWeight: 600 }}
                  fullWidth
                >
                  Overiť návštevu (GPS)
                </Button>
                <Button
                  size="md"
                  variant="outline"
                  color="blue"
                  onClick={() => handlePhotoVerifyClick(place.id)}
                  style={{ fontWeight: 600 }}
                  fullWidth
                >
                  Overiť fotkou
                </Button>
              </Stack>
            )}
          </Card>
        </Popup>
      </Marker>
    );
  })}
  <PhotoVerificationModal
    opened={photoModalOpen}
    onClose={handlePhotoModalClose}
    placeId={selectedPlaceId}
    onSuccess={handlePhotoSuccess}
  />
  </>;
}
