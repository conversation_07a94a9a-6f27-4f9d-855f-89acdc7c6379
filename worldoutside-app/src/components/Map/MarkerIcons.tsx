import L from "leaflet";

export const notFinishedMarkerIcon = L.icon({
  iconUrl: require("../../assets/map-markers/marker-not-finished.svg").default,
  iconSize: [25, 41],
});

export const softVisitedMarkerIcon = L.icon({
  iconUrl: require("../../assets/map-markers/marker-soft.svg").default,
  iconSize: [25, 41],
});

export const finishedMarkerIcon = L.icon({
  iconUrl: require("../../assets/map-markers/marker-finished.svg").default,
  iconSize: [25, 41],
});

export const userMarkerIcon = L.icon({
  iconUrl: require("../../assets/map-markers/marker-user.svg").default,
  iconSize: [25, 41],
});
