import { Modal, Button, FileInput, Stack, Text, Group } from "@mantine/core";
import { useState } from "react";
// @ts-ignore
import EXIF from "exif-js";

interface PhotoVerificationModalProps {
  opened: boolean;
  onClose: () => void;
  placeId: number | null;
  onSuccess: () => void;
}

export default function PhotoVerificationModal({ opened, onClose, placeId, onSuccess }: PhotoVerificationModalProps) {
  const [file, setFile] = useState<File | null>(null);
  const [exifData, setExifData] = useState<any>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleFileChange = (f: File | null) => {
    setFile(f);
    setError(null);
    setExifData(null);
    if (f) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const img = new window.Image();
        img.onload = function () {
          EXIF.getData(img as any, function (this: any) {
            const allExif = EXIF.getAllTags(this);
            console.log(allExif);
            setExifData(allExif);
          });
        };
        img.src = e.target?.result as string;
      };
      reader.readAsDataURL(f);
    }
  };

  const handleSubmit = async () => {
    if (!file || !placeId) return;
    setUploading(true);
    setError(null);
    try {
      const formData = new FormData();
      formData.append("photo", file);
      formData.append("place_id", String(placeId));
      formData.append("exif", JSON.stringify(exifData));
      const res = await fetch(`${process.env.REACT_APP_API_URL}/places/photo-verification`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("wo-token")}`,
        },
        body: formData,
      });
      if (!res.ok) throw new Error("Nepodarilo sa odoslať fotku");
      onSuccess();
      onClose();
    } catch (e: any) {
      setError(e.message || "Chyba pri nahrávaní");
    } finally {
      setUploading(false);
    }
  };

  return (
    <Modal opened={opened} onClose={onClose} title="Overenie fotkou" centered zIndex={3000}>
      <Stack>
        <FileInput
          label="Vyberte fotku"
          accept="image/*"
          value={file}
          onChange={handleFileChange}
          required
        />
        {exifData && (
          <Text size="xs" color="dimmed">
            EXIF: {exifData.GPSLatitude && exifData.GPSLongitude ? `GPS: ${exifData.GPSLatitude}, ${exifData.GPSLongitude}` : "GPS údaje nenájdené"}
          </Text>
        )}
        {error && <Text color="red" size="sm">{error}</Text>}
        <Group justify="flex-end">
          <Button onClick={handleSubmit} loading={uploading} disabled={!file}>
            Odoslať na overenie
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
