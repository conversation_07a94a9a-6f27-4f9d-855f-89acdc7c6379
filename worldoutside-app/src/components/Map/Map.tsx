import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { useEffect, useState } from "react";
import Markers from "./Markers";
import { getPlacesPlannedOptions } from "../../actions/getPlacesPlanned";
import { userMarkerIcon } from "./MarkerIcons";
import { Button, LoadingOverlay } from "@mantine/core";
import { useMutation, useQuery } from "@tanstack/react-query";
import { getPlacesOptions } from "../../actions/getPlaces";
import { mutationVisit } from "../../actions/mutationVisit";

import { submitCoordinates } from "../../actions/submitCoordinates";
import { panic } from "../../misc/notif";

export default function Map() {

  const [mapPosition, setMapPosition] = useState<[number, number]>([0, 0]);
  const [userPosition, setUserPosition] = useState<[number, number]>([0, 0]);
  // Remove local softVisitedIds and verifiedIds, rely on backend state
  const [verifying, setVerifying] = useState<number | null>(null);

  const { data, isPending, refetch } = useQuery(
    getPlacesOptions(mapPosition[0], mapPosition[1]),
  );
  // Fetch planned peaks for current user
  const { data: planned, refetch: refetchPlanned } = useQuery(getPlacesPlannedOptions());

  const postVisitedMutation = useMutation({
    mutationFn: (id: number) => mutationVisit(id),
  });

  useEffect(() => {
    navigator.geolocation.getCurrentPosition((position) => {
      setUserPosition([position.coords.latitude, position.coords.longitude]);
      setMapPosition([position.coords.latitude, position.coords.longitude]);
    });
  }, []);

  const RecenterAutomatically = ({
    mapPosition,
  }: {
    mapPosition: [number, number];
  }) => {
    const map = useMap();

    useEffect(() => {
      map.setView(mapPosition, map.getZoom());
    }, [map, mapPosition]);

    return null;
  };

  function MoveRefetch() {
    const map = useMap();

    useEffect(() => {
      map.options.easeLinearity = 0;

      map.on("dragend", () => {
        setMapPosition([map.getCenter().lat, map.getCenter().lng]);
      });

      return () => {
        map.off("dragend");
      };
    }, [map]);

    return null;
  }


  function handleVisitedClick(id: number) {
    postVisitedMutation.mutate(id, {
      onSuccess: () => {
        refetch(); // Always refetch to get correct visit_type from backend
      },
      // onError is handled by mutationVisit (shows notification)
    });
  }

  async function handleVerifyClick(id: number) {
    setVerifying(id);
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          await submitCoordinates(
            id,
            position.coords.latitude,
            position.coords.longitude
          );
          refetch(); // Always refetch to get correct visit_type from backend
        } catch (e: any) {
          if (e instanceof Error && e.message && e.message.includes("already visited")) {
            panic({
              title: "Návšteva už bola overená",
              message: "Toto miesto ste už overili."
            });
          } else {
            panic({
              title: "Chyba pri overovaní",
              message: e?.message || "Nepodarilo sa overiť návštevu."
            });
          }
        } finally {
          setVerifying(null);
        }
      },
      () => {
        panic({
          title: "Chyba geolokácie",
          message: "Nepodarilo sa získať polohu zariadenia."
        });
        setVerifying(null);
      }
    );
  }


  // Remove postVisitedMutation.isSuccess && refetch();

  return (
    <>
      <LoadingOverlay visible={isPending} zIndex={1000} />

      <MapContainer
        style={{ height: "100%", minHeight: 400, width: "100%" }}
        center={userPosition}
        zoom={14}
        scrollWheelZoom={false}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        <Marker icon={userMarkerIcon} position={userPosition}>
          <Popup>Tu si ty!</Popup>
        </Marker>

        {data && (
          <Markers
            data={data}
            handleVisitedClick={handleVisitedClick}
            handleVerifyClick={handleVerifyClick}
            verifying={verifying}
            verifiedIds={[]}
            softVisitedIds={[]}
            plannedIds={planned ? planned.map((p: any) => p.id) : []}
            refetchPlanned={refetchPlanned}
            refetchAll={refetch}
          />
        )}

        <RecenterAutomatically mapPosition={mapPosition} />

        <MoveRefetch />

        <Button
          onClick={() => setMapPosition(userPosition)}
          style={{ position: "absolute", bottom: 30, right: 10, zIndex: 1000 }}
        >
          My position
        </Button>
      </MapContainer>
    </>
  );
}
