import React from "react";

import { UnstyledButton } from "@mantine/core";

export default function NavItem({
  href,
  children,
}: {
  href: string;
  children: React.ReactNode;
}) {
  return (
    <UnstyledButton
      component="a"
      href={href}
      style={{ flex: 1, height: 56, display: "flex", alignItems: "center", justifyContent: "center", fontWeight: 500, fontSize: 16, color: "#222", textDecoration: "none" }}
    >
      {children}
    </UnstyledButton>
  );
}
