
import NavItem from "./NavItem";
import { Paper, Group, ActionIcon, Tooltip } from "@mantine/core";
import { IconHome, IconMapPin, IconLogout } from "@tabler/icons-react";
import { useNavigate } from "react-router-dom";
import { success } from "../../misc/notif";

function Navigation() {
  const navigate = useNavigate();

  function handleLogout() {
    localStorage.removeItem("wo-token");
    success({ title: "Odhlásenie", message: "<PERSON><PERSON> ste odhlásený." });
    navigate("/prihlasenie");
  }

  return (
    <Paper shadow="xs" radius={0} p={0} withBorder style={{ borderTop: "1px solid #eee" }}>
      <Group justify="space-between" gap={0} style={{ height: 56 }}>
        <NavItem href="/">
          <IconHome size={22} style={{ marginRight: 6 }} /> Domov
        </NavItem>
        <NavItem href="/navstivene">
          <IconMapPin size={22} style={{ marginRight: 6 }} /> Navštívené
        </NavItem>
        <NavItem href="/profil">
          <span role="img" aria-label="profil" style={{ marginRight: 6 }}>👤</span> Profil
        </NavItem>
        <Tooltip label="Odhlásiť sa" position="bottom">
          <ActionIcon onClick={handleLogout} variant="subtle" color="red" size="lg" style={{ marginLeft: 8 }}>
            <IconLogout size={22} />
          </ActionIcon>
        </Tooltip>
      </Group>
    </Paper>
  );
}

export default Navigation;
