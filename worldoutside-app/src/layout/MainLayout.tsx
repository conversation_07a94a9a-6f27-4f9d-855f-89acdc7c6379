

import React from "react";
import { Paper } from "@mantine/core";
import Navigation from "../partials/Navigation/Navigation";



type MainLayoutProps = {
  children: React.ReactNode;
};

export default function MainLayout({ children }: MainLayoutProps) {
  return (
    <div style={{ display: "flex", flexDirection: "column", minHeight: "100vh", height: "100vh" }}>
      <main style={{ flex: 1, height: "100%" }}>{children}</main>
      <Paper shadow="xs" radius={0} p={0} withBorder style={{ borderTop: "1px solid #e5e7eb", height: 56 }}>
        <Navigation />
      </Paper>
    </div>
  );
}
